{"logs": [{"outputFile": "com.sidimohamed.modetaris.app-mergeDebugResources-71:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b7f1bc1db78be7afd443fad33f283878\\transformed\\material-1.12.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,262,351,439,521,616,705,807,917,1004,1064,1130,1226,1292,1353,1458,1522,1594,1652,1726,1788,1842,1955,2015,2076,2135,2213,2337,2418,2500,2600,2685,2770,2906,2987,3070,3201,3284,3370,3432,3486,3552,3629,3708,3779,3862,3931,4007,4088,4156,4260,4351,4429,4522,4619,4693,4772,4870,4930,5018,5084,5172,5260,5322,5390,5453,5519,5624,5730,5825,5930,5996,6054,6138,6227,6303", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,88,87,81,94,88,101,109,86,59,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,81,99,84,84,135,80,82,130,82,85,61,53,65,76,78,70,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83,88,75,72", "endOffsets": "257,346,434,516,611,700,802,912,999,1059,1125,1221,1287,1348,1453,1517,1589,1647,1721,1783,1837,1950,2010,2071,2130,2208,2332,2413,2495,2595,2680,2765,2901,2982,3065,3196,3279,3365,3427,3481,3547,3624,3703,3774,3857,3926,4002,4083,4151,4255,4346,4424,4517,4614,4688,4767,4865,4925,5013,5079,5167,5255,5317,5385,5448,5514,5619,5725,5820,5925,5991,6049,6133,6222,6298,6371"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,72,73,74,78,81,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,213,224,225,227", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3072,3161,3249,3331,3426,4235,4337,4447,7438,7498,7564,7994,8238,14763,14868,14932,15004,15062,15136,15198,15252,15365,15425,15486,15545,15623,15747,15828,15910,16010,16095,16180,16316,16397,16480,16611,16694,16780,16842,16896,16962,17039,17118,17189,17272,17341,17417,17498,17566,17670,17761,17839,17932,18029,18103,18182,18280,18340,18428,18494,18582,18670,18732,18800,18863,18929,19034,19140,19235,19340,19406,20837,21637,21726,21879", "endLines": "5,33,34,35,36,37,45,46,47,72,73,74,78,81,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,213,224,225,227", "endColumns": "12,88,87,81,94,88,101,109,86,59,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,81,99,84,84,135,80,82,130,82,85,61,53,65,76,78,70,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83,88,75,72", "endOffsets": "307,3156,3244,3326,3421,3510,4332,4442,4529,7493,7559,7655,8055,8294,14863,14927,14999,15057,15131,15193,15247,15360,15420,15481,15540,15618,15742,15823,15905,16005,16090,16175,16311,16392,16475,16606,16689,16775,16837,16891,16957,17034,17113,17184,17267,17336,17412,17493,17561,17665,17756,17834,17927,18024,18098,18177,18275,18335,18423,18489,18577,18665,18727,18795,18858,18924,19029,19135,19230,19335,19401,19459,20916,21721,21797,21947"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2c6e88b2ac5e7cd3ee5dd3a7fc4da6d\\transformed\\appcompat-1.6.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2247,2360,2470,2587,2754,2865", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2242,2355,2465,2582,2749,2860,2940"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "312,421,532,640,731,838,958,1042,1121,1212,1305,1400,1494,1594,1687,1782,1876,1967,2058,2144,2257,2358,2454,2567,2677,2794,2961,21557", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "416,527,635,726,833,953,1037,1116,1207,1300,1395,1489,1589,1682,1777,1871,1962,2053,2139,2252,2353,2449,2562,2672,2789,2956,3067,21632"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2b6f0bed5e0bbd2331dfcab783f35fbc\\transformed\\ui-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,294,388,485,571,653,749,836,922,1012,1105,1182,1257,1330,1402,1483,1551", "endColumns": "98,89,93,96,85,81,95,86,85,89,92,76,74,72,71,80,67,119", "endOffsets": "199,289,383,480,566,648,744,831,917,1007,1100,1177,1252,1325,1397,1478,1546,1666"}, "to": {"startLines": "48,49,69,70,71,79,80,200,201,214,215,226,228,229,230,232,233,234", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4534,4633,7161,7255,7352,8060,8142,19624,19711,20921,21011,21802,21952,22027,22100,22273,22354,22422", "endColumns": "98,89,93,96,85,81,95,86,85,89,92,76,74,72,71,80,67,119", "endOffsets": "4628,4718,7250,7347,7433,8137,8233,19706,19792,21006,21099,21874,22022,22095,22167,22349,22417,22537"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\65feab96c843e558e67ad51c84b27304\\transformed\\core-1.13.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "38,39,40,41,42,43,44,231", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3515,3618,3721,3823,3929,4027,4127,22172", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "3613,3716,3818,3924,4022,4122,4230,22268"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50805f74a84fae76deb712ce270e4367\\transformed\\browser-1.4.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,274,388", "endColumns": "106,111,113,107", "endOffsets": "157,269,383,491"}, "to": {"startLines": "68,75,76,77", "startColumns": "4,4,4,4", "startOffsets": "7054,7660,7772,7886", "endColumns": "106,111,113,107", "endOffsets": "7156,7767,7881,7989"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1ad2fa4952c65a27d545d970f7b3ce16\\transformed\\foundation-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,90", "endOffsets": "135,226"}, "to": {"startLines": "235,236", "startColumns": "4,4", "startOffsets": "22542,22627", "endColumns": "84,90", "endOffsets": "22622,22713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cfeebaf529411246011d89557c21ae26\\transformed\\play-services-ads-23.1.0\\res\\values-ne\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,245,292,347,420,494,599,661,790,900,1028,1081,1144,1259,1343,1391,1482,1527,1573,1637,1724,1768", "endColumns": "45,46,54,72,73,104,61,128,109,127,52,62,114,83,47,90,44,45,63,86,43,55", "endOffsets": "244,291,346,419,493,598,660,789,899,1027,1080,1143,1258,1342,1390,1481,1526,1572,1636,1723,1767,1823"}, "to": {"startLines": "197,198,199,202,203,204,205,206,207,208,209,210,211,212,216,217,218,219,220,221,222,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19464,19514,19565,19797,19874,19952,20061,20127,20260,20374,20506,20563,20630,20749,21104,21156,21251,21300,21350,21418,21509,22718", "endColumns": "49,50,58,76,77,108,65,132,113,131,56,66,118,87,51,94,48,49,67,90,47,59", "endOffsets": "19509,19560,19619,19869,19947,20056,20122,20255,20369,20501,20558,20625,20744,20832,21151,21246,21295,21345,21413,21504,21552,22773"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d1984713afaf7e95791b7113f0c3d099\\transformed\\play-services-base-18.0.0\\res\\values-ne\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,304,458,588,701,868,1000,1106,1207,1383,1493,1653,1782,1926,2074,2136,2204", "endColumns": "110,153,129,112,166,131,105,100,175,109,159,128,143,147,61,67,87", "endOffsets": "303,457,587,700,867,999,1105,1206,1382,1492,1652,1781,1925,2073,2135,2203,2291"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4723,4838,4996,5130,5247,5418,5554,5664,5933,6113,6227,6391,6524,6672,6824,6890,6962", "endColumns": "114,157,133,116,170,135,109,104,179,113,163,132,147,151,65,71,91", "endOffsets": "4833,4991,5125,5242,5413,5549,5659,5764,6108,6222,6386,6519,6667,6819,6885,6957,7049"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\828443dfe1a0469ecc410a4d0bc8bbd4\\transformed\\play-services-basement-18.3.0\\res\\values-ne\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5769", "endColumns": "163", "endOffsets": "5928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4b91d173b095b76b27cd7d7591362329\\transformed\\material3-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,303,419,547,646,741,853,1005,1126,1279,1363,1471,1569,1668,1780,1904,2017,2163,2306,2440,2605,2735,2887,3044,3173,3272,3367,3483,3607,3711,3830,3940,4086,4234,4344,4452,4527,4632,4737,4848,4939,5034,5141,5221,5306,5407,5516,5611,5714,5801,5912,6011,6116,6239,6319,6425", "endColumns": "128,118,115,127,98,94,111,151,120,152,83,107,97,98,111,123,112,145,142,133,164,129,151,156,128,98,94,115,123,103,118,109,145,147,109,107,74,104,104,110,90,94,106,79,84,100,108,94,102,86,110,98,104,122,79,105,93", "endOffsets": "179,298,414,542,641,736,848,1000,1121,1274,1358,1466,1564,1663,1775,1899,2012,2158,2301,2435,2600,2730,2882,3039,3168,3267,3362,3478,3602,3706,3825,3935,4081,4229,4339,4447,4522,4627,4732,4843,4934,5029,5136,5216,5301,5402,5511,5606,5709,5796,5907,6006,6111,6234,6314,6420,6514"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8299,8428,8547,8663,8791,8890,8985,9097,9249,9370,9523,9607,9715,9813,9912,10024,10148,10261,10407,10550,10684,10849,10979,11131,11288,11417,11516,11611,11727,11851,11955,12074,12184,12330,12478,12588,12696,12771,12876,12981,13092,13183,13278,13385,13465,13550,13651,13760,13855,13958,14045,14156,14255,14360,14483,14563,14669", "endColumns": "128,118,115,127,98,94,111,151,120,152,83,107,97,98,111,123,112,145,142,133,164,129,151,156,128,98,94,115,123,103,118,109,145,147,109,107,74,104,104,110,90,94,106,79,84,100,108,94,102,86,110,98,104,122,79,105,93", "endOffsets": "8423,8542,8658,8786,8885,8980,9092,9244,9365,9518,9602,9710,9808,9907,10019,10143,10256,10402,10545,10679,10844,10974,11126,11283,11412,11511,11606,11722,11846,11950,12069,12179,12325,12473,12583,12691,12766,12871,12976,13087,13178,13273,13380,13460,13545,13646,13755,13850,13953,14040,14151,14250,14355,14478,14558,14664,14758"}}]}]}