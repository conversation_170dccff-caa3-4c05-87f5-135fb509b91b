{"logs": [{"outputFile": "com.sidimohamed.modetaris.app-mergeDebugResources-71:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1ad2fa4952c65a27d545d970f7b3ce16\\transformed\\foundation-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,149", "endColumns": "93,95", "endOffsets": "144,240"}, "to": {"startLines": "235,236", "startColumns": "4,4", "startOffsets": "22274,22368", "endColumns": "93,95", "endOffsets": "22363,22459"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4b91d173b095b76b27cd7d7591362329\\transformed\\material3-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,292,408,526,623,718,830,963,1084,1232,1317,1416,1510,1606,1721,1845,1949,2094,2238,2380,2554,2685,2806,2933,3058,3153,3251,3377,3512,3612,3714,3827,3968,4117,4233,4335,4412,4506,4601,4720,4812,4898,5012,5095,5178,5278,5380,5477,5574,5662,5769,5869,5971,6104,6187,6298", "endColumns": "118,117,115,117,96,94,111,132,120,147,84,98,93,95,114,123,103,144,143,141,173,130,120,126,124,94,97,125,134,99,101,112,140,148,115,101,76,93,94,118,91,85,113,82,82,99,101,96,96,87,106,99,101,132,82,110,102", "endOffsets": "169,287,403,521,618,713,825,958,1079,1227,1312,1411,1505,1601,1716,1840,1944,2089,2233,2375,2549,2680,2801,2928,3053,3148,3246,3372,3507,3607,3709,3822,3963,4112,4228,4330,4407,4501,4596,4715,4807,4893,5007,5090,5173,5273,5375,5472,5569,5657,5764,5864,5966,6099,6182,6293,6396"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8167,8286,8404,8520,8638,8735,8830,8942,9075,9196,9344,9429,9528,9622,9718,9833,9957,10061,10206,10350,10492,10666,10797,10918,11045,11170,11265,11363,11489,11624,11724,11826,11939,12080,12229,12345,12447,12524,12618,12713,12832,12924,13010,13124,13207,13290,13390,13492,13589,13686,13774,13881,13981,14083,14216,14299,14410", "endColumns": "118,117,115,117,96,94,111,132,120,147,84,98,93,95,114,123,103,144,143,141,173,130,120,126,124,94,97,125,134,99,101,112,140,148,115,101,76,93,94,118,91,85,113,82,82,99,101,96,96,87,106,99,101,132,82,110,102", "endOffsets": "8281,8399,8515,8633,8730,8825,8937,9070,9191,9339,9424,9523,9617,9713,9828,9952,10056,10201,10345,10487,10661,10792,10913,11040,11165,11260,11358,11484,11619,11719,11821,11934,12075,12224,12340,12442,12519,12613,12708,12827,12919,13005,13119,13202,13285,13385,13487,13584,13681,13769,13876,13976,14078,14211,14294,14405,14508"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\65feab96c843e558e67ad51c84b27304\\transformed\\core-1.13.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "38,39,40,41,42,43,44,231", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3465,3563,3665,3762,3860,3965,4068,21900", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "3558,3660,3757,3855,3960,4063,4179,21996"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2b6f0bed5e0bbd2331dfcab783f35fbc\\transformed\\ui-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,299,395,498,583,660,750,842,926,1010,1099,1171,1248,1326,1402,1483,1554", "endColumns": "103,89,95,102,84,76,89,91,83,83,88,71,76,77,75,80,70,120", "endOffsets": "204,294,390,493,578,655,745,837,921,1005,1094,1166,1243,1321,1397,1478,1549,1670"}, "to": {"startLines": "48,49,69,70,71,79,80,200,201,214,215,226,228,229,230,232,233,234", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4491,4595,7041,7137,7240,7936,8013,19383,19475,20653,20737,21518,21669,21746,21824,22001,22082,22153", "endColumns": "103,89,95,102,84,76,89,91,83,83,88,71,76,77,75,80,70,120", "endOffsets": "4590,4680,7132,7235,7320,8008,8098,19470,19554,20732,20821,21585,21741,21819,21895,22077,22148,22269"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50805f74a84fae76deb712ce270e4367\\transformed\\browser-1.4.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,273,388", "endColumns": "112,104,114,100", "endOffsets": "163,268,383,484"}, "to": {"startLines": "68,75,76,77", "startColumns": "4,4,4,4", "startOffsets": "6928,7545,7650,7765", "endColumns": "112,104,114,100", "endOffsets": "7036,7645,7760,7861"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b7f1bc1db78be7afd443fad33f283878\\transformed\\material-1.12.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,352,432,514,611,700,796,920,1007,1070,1136,1227,1297,1361,1464,1527,1592,1652,1720,1783,1838,1966,2023,2085,2140,2215,2355,2442,2521,2614,2700,2783,2916,2998,3083,3229,3316,3393,3447,3502,3568,3641,3717,3788,3866,3939,4015,4090,4160,4269,4357,4432,4524,4616,4690,4764,4856,4909,4991,5058,5141,5228,5290,5354,5417,5487,5601,5716,5818,5930,5988,6047,6132,6221,6305", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,81,96,88,95,123,86,62,65,90,69,63,102,62,64,59,67,62,54,127,56,61,54,74,139,86,78,92,85,82,132,81,84,145,86,76,53,54,65,72,75,70,77,72,75,74,69,108,87,74,91,91,73,73,91,52,81,66,82,86,61,63,62,69,113,114,101,111,57,58,84,88,83,78", "endOffsets": "266,347,427,509,606,695,791,915,1002,1065,1131,1222,1292,1356,1459,1522,1587,1647,1715,1778,1833,1961,2018,2080,2135,2210,2350,2437,2516,2609,2695,2778,2911,2993,3078,3224,3311,3388,3442,3497,3563,3636,3712,3783,3861,3934,4010,4085,4155,4264,4352,4427,4519,4611,4685,4759,4851,4904,4986,5053,5136,5223,5285,5349,5412,5482,5596,5711,5813,5925,5983,6042,6127,6216,6300,6379"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,72,73,74,78,81,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,213,224,225,227", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3036,3117,3197,3279,3376,4184,4280,4404,7325,7388,7454,7866,8103,14513,14616,14679,14744,14804,14872,14935,14990,15118,15175,15237,15292,15367,15507,15594,15673,15766,15852,15935,16068,16150,16235,16381,16468,16545,16599,16654,16720,16793,16869,16940,17018,17091,17167,17242,17312,17421,17509,17584,17676,17768,17842,17916,18008,18061,18143,18210,18293,18380,18442,18506,18569,18639,18753,18868,18970,19082,19140,20568,21345,21434,21590", "endLines": "5,33,34,35,36,37,45,46,47,72,73,74,78,81,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,213,224,225,227", "endColumns": "12,80,79,81,96,88,95,123,86,62,65,90,69,63,102,62,64,59,67,62,54,127,56,61,54,74,139,86,78,92,85,82,132,81,84,145,86,76,53,54,65,72,75,70,77,72,75,74,69,108,87,74,91,91,73,73,91,52,81,66,82,86,61,63,62,69,113,114,101,111,57,58,84,88,83,78", "endOffsets": "316,3112,3192,3274,3371,3460,4275,4399,4486,7383,7449,7540,7931,8162,14611,14674,14739,14799,14867,14930,14985,15113,15170,15232,15287,15362,15502,15589,15668,15761,15847,15930,16063,16145,16230,16376,16463,16540,16594,16649,16715,16788,16864,16935,17013,17086,17162,17237,17307,17416,17504,17579,17671,17763,17837,17911,18003,18056,18138,18205,18288,18375,18437,18501,18564,18634,18748,18863,18965,19077,19135,19194,20648,21429,21513,21664"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d1984713afaf7e95791b7113f0c3d099\\transformed\\play-services-base-18.0.0\\res\\values-mk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,453,582,688,829,958,1074,1180,1333,1436,1598,1727,1876,2031,2096,2156", "endColumns": "102,156,128,105,140,128,115,105,152,102,161,128,148,154,64,59,74", "endOffsets": "295,452,581,687,828,957,1073,1179,1332,1435,1597,1726,1875,2030,2095,2155,2230"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4685,4792,4953,5086,5196,5341,5474,5594,5841,5998,6105,6271,6404,6557,6716,6785,6849", "endColumns": "106,160,132,109,144,132,119,109,156,106,165,132,152,158,68,63,78", "endOffsets": "4787,4948,5081,5191,5336,5469,5589,5699,5993,6100,6266,6399,6552,6711,6780,6844,6923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2c6e88b2ac5e7cd3ee5dd3a7fc4da6d\\transformed\\appcompat-1.6.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,903,994,1087,1183,1277,1377,1470,1565,1661,1752,1843,1930,2036,2142,2243,2350,2462,2566,2722,2820", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "208,312,420,506,614,733,817,898,989,1082,1178,1272,1372,1465,1560,1656,1747,1838,1925,2031,2137,2238,2345,2457,2561,2717,2815,2900"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,533,641,727,835,954,1038,1119,1210,1303,1399,1493,1593,1686,1781,1877,1968,2059,2146,2252,2358,2459,2566,2678,2782,2938,21260", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "424,528,636,722,830,949,1033,1114,1205,1298,1394,1488,1588,1681,1776,1872,1963,2054,2141,2247,2353,2454,2561,2673,2777,2933,3031,21340"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\828443dfe1a0469ecc410a4d0bc8bbd4\\transformed\\play-services-basement-18.3.0\\res\\values-mk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "132", "endOffsets": "327"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5704", "endColumns": "136", "endOffsets": "5836"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cfeebaf529411246011d89557c21ae26\\transformed\\play-services-ads-23.1.0\\res\\values-mk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,248,295,371,436,507,610,673,789,895,1018,1072,1128,1237,1336,1382,1483,1518,1551,1606,1693,1742", "endColumns": "48,46,75,64,70,102,62,115,105,122,53,55,108,98,45,100,34,32,54,86,48,55", "endOffsets": "247,294,370,435,506,609,672,788,894,1017,1071,1127,1236,1335,1381,1482,1517,1550,1605,1692,1741,1797"}, "to": {"startLines": "197,198,199,202,203,204,205,206,207,208,209,210,211,212,216,217,218,219,220,221,222,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19199,19252,19303,19559,19628,19703,19810,19877,19997,20107,20234,20292,20352,20465,20826,20876,20981,21020,21057,21116,21207,22464", "endColumns": "52,50,79,68,74,106,66,119,109,126,57,59,112,102,49,104,38,36,58,90,52,59", "endOffsets": "19247,19298,19378,19623,19698,19805,19872,19992,20102,20229,20287,20347,20460,20563,20871,20976,21015,21052,21111,21202,21255,22519"}}]}]}