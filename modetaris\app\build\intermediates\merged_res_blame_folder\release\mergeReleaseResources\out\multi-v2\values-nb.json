{"logs": [{"outputFile": "com.sidimohamed.modetaris.app-mergeReleaseResources-71:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\14e7f68a259f3c37a361ba30fe5c1f4e\\transformed\\material-1.12.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,586,666,765,884,966,1025,1089,1181,1249,1309,1396,1460,1522,1586,1654,1719,1773,1882,1940,2002,2056,2131,2251,2333,2410,2500,2584,2664,2798,2876,2956,3079,3167,3245,3299,3350,3416,3484,3558,3629,3705,3776,3854,3924,3994,4094,4183,4261,4349,4439,4511,4583,4667,4718,4796,4862,4943,5026,5088,5152,5215,5284,5384,5488,5581,5681,5739,5794,5872,5956,6034", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "256,333,406,493,581,661,760,879,961,1020,1084,1176,1244,1304,1391,1455,1517,1581,1649,1714,1768,1877,1935,1997,2051,2126,2246,2328,2405,2495,2579,2659,2793,2871,2951,3074,3162,3240,3294,3345,3411,3479,3553,3624,3700,3771,3849,3919,3989,4089,4178,4256,4344,4434,4506,4578,4662,4713,4791,4857,4938,5021,5083,5147,5210,5279,5379,5483,5576,5676,5734,5789,5867,5951,6029,6101"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,72,73,74,78,81,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,213,224,225,227", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2943,3020,3093,3180,3268,4074,4173,4292,7157,7216,7280,7682,7914,14114,14201,14265,14327,14391,14459,14524,14578,14687,14745,14807,14861,14936,15056,15138,15215,15305,15389,15469,15603,15681,15761,15884,15972,16050,16104,16155,16221,16289,16363,16434,16510,16581,16659,16729,16799,16899,16988,17066,17154,17244,17316,17388,17472,17523,17601,17667,17748,17831,17893,17957,18020,18089,18189,18293,18386,18486,18544,19880,20579,20663,20811", "endLines": "5,33,34,35,36,37,45,46,47,72,73,74,78,81,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,213,224,225,227", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "306,3015,3088,3175,3263,3343,4168,4287,4369,7211,7275,7367,7745,7969,14196,14260,14322,14386,14454,14519,14573,14682,14740,14802,14856,14931,15051,15133,15210,15300,15384,15464,15598,15676,15756,15879,15967,16045,16099,16150,16216,16284,16358,16429,16505,16576,16654,16724,16794,16894,16983,17061,17149,17239,17311,17383,17467,17518,17596,17662,17743,17826,17888,17952,18015,18084,18184,18288,18381,18481,18539,18594,19953,20658,20736,20878"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c98991d4124df242de128fef04415490\\transformed\\play-services-base-18.0.1\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4548,4654,4813,4939,5048,5204,5334,5454,5687,5841,5948,6109,6237,6379,6555,6622,6684", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "4649,4808,4934,5043,5199,5329,5449,5552,5836,5943,6104,6232,6374,6550,6617,6679,6757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7a7dd9a91e8c8fc741f16bc5a65bae96\\transformed\\core-1.13.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "38,39,40,41,42,43,44,231", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3348,3442,3544,3641,3740,3848,3954,21098", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3437,3539,3636,3735,3843,3949,4069,21194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\35572cd30262c536cf2e7a5a304052f5\\transformed\\appcompat-1.6.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,414,509,623,709,809,922,999,1074,1165,1258,1352,1446,1546,1639,1734,1832,1923,2014,2092,2195,2293,2389,2493,2592,2693,2846,20499", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "409,504,618,704,804,917,994,1069,1160,1253,1347,1441,1541,1634,1729,1827,1918,2009,2087,2190,2288,2384,2488,2587,2688,2841,2938,20574"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8e1bbd0b8bc21648be71744092394980\\transformed\\play-services-basement-18.3.0\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5557", "endColumns": "129", "endOffsets": "5682"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c52dd95a1bc816de48598e07baf79184\\transformed\\ui-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,279,376,476,564,640,728,817,899,979,1061,1131,1205,1276,1346,1423,1490", "endColumns": "92,80,96,99,87,75,87,88,81,79,81,69,73,70,69,76,66,119", "endOffsets": "193,274,371,471,559,635,723,812,894,974,1056,1126,1200,1271,1341,1418,1485,1605"}, "to": {"startLines": "48,49,69,70,71,79,80,200,201,214,215,226,228,229,230,232,233,234", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4374,4467,6872,6969,7069,7750,7826,18759,18848,19958,20038,20741,20883,20957,21028,21199,21276,21343", "endColumns": "92,80,96,99,87,75,87,88,81,79,81,69,73,70,69,76,66,119", "endOffsets": "4462,4543,6964,7064,7152,7821,7909,18843,18925,20033,20115,20806,20952,21023,21093,21271,21338,21458"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b4cde7b2dd1f5775a904881f0ce4c78a\\transformed\\foundation-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,87", "endOffsets": "140,228"}, "to": {"startLines": "235,236", "startColumns": "4,4", "startOffsets": "21463,21553", "endColumns": "89,87", "endOffsets": "21548,21636"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6cf0b63964c367f4ee81bf42324e770d\\transformed\\material3-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,394,507,606,700,811,955,1077,1227,1311,1411,1500,1594,1701,1819,1924,2051,2173,2306,2473,2600,2716,2837,2958,3048,3146,3265,3396,3497,3607,3710,3844,3985,4090,4188,4268,4362,4453,4562,4646,4730,4841,4921,5005,5106,5205,5296,5396,5484,5589,5691,5796,5913,5993,6096", "endColumns": "116,114,106,112,98,93,110,143,121,149,83,99,88,93,106,117,104,126,121,132,166,126,115,120,120,89,97,118,130,100,109,102,133,140,104,97,79,93,90,108,83,83,110,79,83,100,98,90,99,87,104,101,104,116,79,102,98", "endOffsets": "167,282,389,502,601,695,806,950,1072,1222,1306,1406,1495,1589,1696,1814,1919,2046,2168,2301,2468,2595,2711,2832,2953,3043,3141,3260,3391,3492,3602,3705,3839,3980,4085,4183,4263,4357,4448,4557,4641,4725,4836,4916,5000,5101,5200,5291,5391,5479,5584,5686,5791,5908,5988,6091,6190"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7974,8091,8206,8313,8426,8525,8619,8730,8874,8996,9146,9230,9330,9419,9513,9620,9738,9843,9970,10092,10225,10392,10519,10635,10756,10877,10967,11065,11184,11315,11416,11526,11629,11763,11904,12009,12107,12187,12281,12372,12481,12565,12649,12760,12840,12924,13025,13124,13215,13315,13403,13508,13610,13715,13832,13912,14015", "endColumns": "116,114,106,112,98,93,110,143,121,149,83,99,88,93,106,117,104,126,121,132,166,126,115,120,120,89,97,118,130,100,109,102,133,140,104,97,79,93,90,108,83,83,110,79,83,100,98,90,99,87,104,101,104,116,79,102,98", "endOffsets": "8086,8201,8308,8421,8520,8614,8725,8869,8991,9141,9225,9325,9414,9508,9615,9733,9838,9965,10087,10220,10387,10514,10630,10751,10872,10962,11060,11179,11310,11411,11521,11624,11758,11899,12004,12102,12182,12276,12367,12476,12560,12644,12755,12835,12919,13020,13119,13210,13310,13398,13503,13605,13710,13827,13907,14010,14109"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8a119d90c118efe13bee301fab084f4\\transformed\\play-services-ads-23.1.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,292,347,411,480,570,632,738,842,954,1004,1060,1167,1253,1292,1369,1402,1435,1488,1565,1604", "endColumns": "41,50,54,63,68,89,61,105,103,111,49,55,106,85,38,76,32,32,52,76,38,55", "endOffsets": "240,291,346,410,479,569,631,737,841,953,1003,1059,1166,1252,1291,1368,1401,1434,1487,1564,1603,1659"}, "to": {"startLines": "197,198,199,202,203,204,205,206,207,208,209,210,211,212,216,217,218,219,220,221,222,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18599,18645,18700,18930,18998,19071,19165,19231,19341,19449,19565,19619,19679,19790,20120,20163,20244,20281,20318,20375,20456,21641", "endColumns": "45,54,58,67,72,93,65,109,107,115,53,59,110,89,42,80,36,36,56,80,42,59", "endOffsets": "18640,18695,18754,18993,19066,19160,19226,19336,19444,19560,19614,19674,19785,19875,20158,20239,20276,20313,20370,20451,20494,21696"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\137d2217feb65c58c9968fa3c5225eb7\\transformed\\browser-1.4.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "68,75,76,77", "startColumns": "4,4,4,4", "startOffsets": "6762,7372,7473,7585", "endColumns": "109,100,111,96", "endOffsets": "6867,7468,7580,7677"}}]}]}