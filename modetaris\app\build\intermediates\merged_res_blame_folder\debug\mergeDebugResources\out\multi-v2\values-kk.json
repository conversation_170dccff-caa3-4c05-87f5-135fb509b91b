{"logs": [{"outputFile": "com.sidimohamed.modetaris.app-mergeDebugResources-71:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50805f74a84fae76deb712ce270e4367\\transformed\\browser-1.4.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,259,367", "endColumns": "99,103,107,104", "endOffsets": "150,254,362,467"}, "to": {"startLines": "68,75,76,77", "startColumns": "4,4,4,4", "startOffsets": "6822,7435,7539,7647", "endColumns": "99,103,107,104", "endOffsets": "6917,7534,7642,7747"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4b91d173b095b76b27cd7d7591362329\\transformed\\material3-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,291,406,522,624,725,843,981,1106,1231,1315,1418,1508,1605,1721,1845,1953,2095,2235,2367,2526,2649,2764,2883,2998,3089,3187,3310,3445,3549,3660,3766,3905,4050,4158,4258,4344,4437,4530,4638,4724,4808,4912,5001,5086,5187,5291,5388,5484,5571,5675,5774,5872,6009,6099,6210", "endColumns": "117,117,114,115,101,100,117,137,124,124,83,102,89,96,115,123,107,141,139,131,158,122,114,118,114,90,97,122,134,103,110,105,138,144,107,99,85,92,92,107,85,83,103,88,84,100,103,96,95,86,103,98,97,136,89,110,100", "endOffsets": "168,286,401,517,619,720,838,976,1101,1226,1310,1413,1503,1600,1716,1840,1948,2090,2230,2362,2521,2644,2759,2878,2993,3084,3182,3305,3440,3544,3655,3761,3900,4045,4153,4253,4339,4432,4525,4633,4719,4803,4907,4996,5081,5182,5286,5383,5479,5566,5670,5769,5867,6004,6094,6205,6306"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8059,8177,8295,8410,8526,8628,8729,8847,8985,9110,9235,9319,9422,9512,9609,9725,9849,9957,10099,10239,10371,10530,10653,10768,10887,11002,11093,11191,11314,11449,11553,11664,11770,11909,12054,12162,12262,12348,12441,12534,12642,12728,12812,12916,13005,13090,13191,13295,13392,13488,13575,13679,13778,13876,14013,14103,14214", "endColumns": "117,117,114,115,101,100,117,137,124,124,83,102,89,96,115,123,107,141,139,131,158,122,114,118,114,90,97,122,134,103,110,105,138,144,107,99,85,92,92,107,85,83,103,88,84,100,103,96,95,86,103,98,97,136,89,110,100", "endOffsets": "8172,8290,8405,8521,8623,8724,8842,8980,9105,9230,9314,9417,9507,9604,9720,9844,9952,10094,10234,10366,10525,10648,10763,10882,10997,11088,11186,11309,11444,11548,11659,11765,11904,12049,12157,12257,12343,12436,12529,12637,12723,12807,12911,13000,13085,13186,13290,13387,13483,13570,13674,13773,13871,14008,14098,14209,14310"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1ad2fa4952c65a27d545d970f7b3ce16\\transformed\\foundation-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,147", "endColumns": "91,95", "endOffsets": "142,238"}, "to": {"startLines": "235,236", "startColumns": "4,4", "startOffsets": "22007,22099", "endColumns": "91,95", "endOffsets": "22094,22190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2c6e88b2ac5e7cd3ee5dd3a7fc4da6d\\transformed\\appcompat-1.6.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,536,646,731,837,956,1036,1113,1204,1297,1392,1486,1586,1679,1774,1871,1962,2053,2134,2239,2342,2440,2547,2653,2753,2919,21024", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "426,531,641,726,832,951,1031,1108,1199,1292,1387,1481,1581,1674,1769,1866,1957,2048,2129,2234,2337,2435,2542,2648,2748,2914,3009,21101"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cfeebaf529411246011d89557c21ae26\\transformed\\play-services-ads-23.1.0\\res\\values-kk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,250,299,356,425,496,609,674,817,934,1065,1120,1179,1293,1381,1424,1518,1554,1592,1643,1726,1767", "endColumns": "50,48,56,68,70,112,64,142,116,130,54,58,113,87,42,93,35,37,50,82,40,55", "endOffsets": "249,298,355,424,495,608,673,816,933,1064,1119,1178,1292,1380,1423,1517,1553,1591,1642,1725,1766,1822"}, "to": {"startLines": "197,198,199,202,203,204,205,206,207,208,209,210,211,212,216,217,218,219,220,221,222,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18948,19003,19056,19289,19362,19437,19554,19623,19770,19891,20026,20085,20148,20266,20610,20657,20755,20795,20837,20892,20979,22195", "endColumns": "54,52,60,72,74,116,68,146,120,134,58,62,117,91,46,97,39,41,54,86,44,59", "endOffsets": "18998,19051,19112,19357,19432,19549,19618,19765,19886,20021,20080,20143,20261,20353,20652,20750,20790,20832,20887,20974,21019,22250"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b7f1bc1db78be7afd443fad33f283878\\transformed\\material-1.12.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,427,506,600,688,780,892,974,1034,1098,1193,1263,1326,1433,1498,1565,1626,1693,1755,1809,1923,1982,2043,2097,2172,2298,2386,2472,2573,2663,2753,2895,2967,3040,3177,3266,3347,3404,3460,3526,3597,3674,3745,3825,3897,3973,4054,4124,4224,4311,4383,4474,4567,4641,4716,4808,4860,4942,5008,5092,5178,5240,5304,5367,5436,5540,5644,5738,5838,5899,5959,6043,6127,6203", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,78,93,87,91,111,81,59,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,85,100,89,89,141,71,72,136,88,80,56,55,65,70,76,70,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,103,93,99,60,59,83,83,75,78", "endOffsets": "268,346,422,501,595,683,775,887,969,1029,1093,1188,1258,1321,1428,1493,1560,1621,1688,1750,1804,1918,1977,2038,2092,2167,2293,2381,2467,2568,2658,2748,2890,2962,3035,3172,3261,3342,3399,3455,3521,3592,3669,3740,3820,3892,3968,4049,4119,4219,4306,4378,4469,4562,4636,4711,4803,4855,4937,5003,5087,5173,5235,5299,5362,5431,5535,5639,5733,5833,5894,5954,6038,6122,6198,6277"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,72,73,74,78,81,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,213,224,225,227", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3014,3092,3168,3247,3341,4143,4235,4347,7216,7276,7340,7752,7996,14315,14422,14487,14554,14615,14682,14744,14798,14912,14971,15032,15086,15161,15287,15375,15461,15562,15652,15742,15884,15956,16029,16166,16255,16336,16393,16449,16515,16586,16663,16734,16814,16886,16962,17043,17113,17213,17300,17372,17463,17556,17630,17705,17797,17849,17931,17997,18081,18167,18229,18293,18356,18425,18529,18633,18727,18827,18888,20358,21106,21190,21339", "endLines": "5,33,34,35,36,37,45,46,47,72,73,74,78,81,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,213,224,225,227", "endColumns": "12,77,75,78,93,87,91,111,81,59,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,85,100,89,89,141,71,72,136,88,80,56,55,65,70,76,70,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,103,93,99,60,59,83,83,75,78", "endOffsets": "318,3087,3163,3242,3336,3424,4230,4342,4424,7271,7335,7430,7817,8054,14417,14482,14549,14610,14677,14739,14793,14907,14966,15027,15081,15156,15282,15370,15456,15557,15647,15737,15879,15951,16024,16161,16250,16331,16388,16444,16510,16581,16658,16729,16809,16881,16957,17038,17108,17208,17295,17367,17458,17551,17625,17700,17792,17844,17926,17992,18076,18162,18224,18288,18351,18420,18524,18628,18722,18822,18883,18943,20437,21185,21261,21413"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d1984713afaf7e95791b7113f0c3d099\\transformed\\play-services-base-18.0.0\\res\\values-kk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,441,563,665,801,923,1042,1147,1308,1410,1563,1688,1837,1990,2049,2104", "endColumns": "98,148,121,101,135,121,118,104,160,101,152,124,148,152,58,54,73", "endOffsets": "291,440,562,664,800,922,1041,1146,1307,1409,1562,1687,1836,1989,2048,2103,2177"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4605,4708,4861,4987,5093,5233,5359,5482,5755,5920,6026,6183,6312,6465,6622,6685,6744", "endColumns": "102,152,125,105,139,125,122,108,164,105,156,128,152,156,62,58,77", "endOffsets": "4703,4856,4982,5088,5228,5354,5477,5586,5915,6021,6178,6307,6460,6617,6680,6739,6817"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\828443dfe1a0469ecc410a4d0bc8bbd4\\transformed\\play-services-basement-18.3.0\\res\\values-kk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5591", "endColumns": "163", "endOffsets": "5750"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\65feab96c843e558e67ad51c84b27304\\transformed\\core-1.13.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "38,39,40,41,42,43,44,231", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3429,3524,3626,3728,3831,3935,4032,21642", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "3519,3621,3723,3826,3930,4027,4138,21738"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2b6f0bed5e0bbd2331dfcab783f35fbc\\transformed\\ui-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,386,488,575,656,749,839,921,1004,1089,1162,1236,1312,1386,1462,1532", "endColumns": "92,82,104,101,86,80,92,89,81,82,84,72,73,75,73,75,69,117", "endOffsets": "193,276,381,483,570,651,744,834,916,999,1084,1157,1231,1307,1381,1457,1527,1645"}, "to": {"startLines": "48,49,69,70,71,79,80,200,201,214,215,226,228,229,230,232,233,234", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4429,4522,6922,7027,7129,7822,7903,19117,19207,20442,20525,21266,21418,21492,21568,21743,21819,21889", "endColumns": "92,82,104,101,86,80,92,89,81,82,84,72,73,75,73,75,69,117", "endOffsets": "4517,4600,7022,7124,7211,7898,7991,19202,19284,20520,20605,21334,21487,21563,21637,21814,21884,22002"}}]}]}