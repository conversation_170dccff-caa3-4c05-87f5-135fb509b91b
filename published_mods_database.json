{"zombiegirls|bythebot03": {"mod_name": "ZombieGirls", "creator_name": "ByTheBot03", "source_url": "", "download_url": "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/https%3A%2F%2Fstorage.googleapis.com%2Fdownload-e33a2.firebasestorage.app%2Fmods%2FZombieGirls_1751890456_tivf7aft.mcpack?alt=media", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1751890467.6008556, "published_date": "2025-07-07 12:14:27"}, "revive me revive loot saver chest|bycarchi77": {"mod_name": "Revive Me! - Revive & Loot Saver Chest", "creator_name": "ByCarchi77", "source_url": "", "download_url": "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/https%3A%2F%2Fstorage.googleapis.com%2Fdownload-e33a2.firebasestorage.app%2Fmods%2FRevive_Me_115_-_1219x_1752006870_zwyxudrl.mcaddon?alt=media", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1752006974.6166992, "published_date": "2025-07-08 20:36:14"}, "phase blocks walk through any block|byfour worlds studios": {"mod_name": "Phase Blocks (Walk through any Block!)", "creator_name": "ByFour Worlds Studios", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/combined_modbin_1754065257_14s7pv0x.mcpack", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754065660.1220155, "published_date": "2025-08-01 16:27:40"}, "mc but you get any block you look at|bybony162": {"mod_name": "MC But You Get Any Block You Look At", "creator_name": "ByBONY162", "source_url": "", "download_url": "https://media.forgecdn.net/attachments/description/null/description_c68ac84d-44cd-4469-845c-7363451fea2d.png", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754078817.1977885, "published_date": "2025-08-01 20:06:57"}, "bare bones stuff|by pochilito": {"mod_name": "Bare Bones & Stuff", "creator_name": "By Pochilit<PERSON>", "source_url": "", "download_url": "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/mods%2FBarebonesNStuff_15_V2_1754167401_zbogefk4.mcpack?alt=mediahttps://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/mods%2FBarebonesNStuff_15_V2_1754167401_zbogefk4.mcpack?alt=media", "mod_size": null, "version": null, "category": "Texture Pack", "description": null, "published_at": 1754167632.1239896, "published_date": "2025-08-02 20:47:12"}, "bare bones stuff|bypochilito": {"mod_name": "Bare Bones & Stuff", "creator_name": "ByPochilito", "source_url": "", "download_url": "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/mods%2FBarebonesNStuff_15_V2_1754169255_1w2yoc1j.mcpack?alt=media", "mod_size": null, "version": null, "category": "Texture Pack", "description": null, "published_at": 1754169275.4753733, "published_date": "2025-08-02 21:14:35"}, "glowshot projectile trails|just flaash": {"mod_name": "Glow-Shot Projectile Trails", "creator_name": "<PERSON>", "source_url": "", "download_url": "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/mods%2FGlow-Shot_1754214663_6zpf42g4.mcpack?alt=media", "mod_size": null, "version": null, "category": "Texture Pack", "description": null, "published_at": 1754214780.3743997, "published_date": "2025-08-03 09:53:00"}, "armored ghast addon happy ghast equipment|reyro project": {"mod_name": "Armored Ghast [Addon] [Happy Ghast] [Equipment]", "creator_name": "Reyro Project", "source_url": "", "download_url": "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/mods%2FArmGhast_1754216274_1s1vxvxj.mcaddon?alt=media", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754216398.170992, "published_date": "2025-08-03 10:19:58"}, "aplok guns|gabrielaplok": {"mod_name": "Aplok Guns", "creator_name": "GabrielAplok", "source_url": "", "download_url": "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/mods%2FAplok-Guns-v1.1.7.mcaddon?alt=media&token=166a2024-a8c7-4eff-90ae-51e42beed95d", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754218514.3912, "published_date": "2025-08-03 10:55:14"}, "horror environment achievement friendly update|byzorrocraft1": {"mod_name": "Horror Environment - Achievement friendly Update", "creator_name": "ByZorrocraft1", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Aplok-Guns-v117_1754232953_mxhi1phs.mcaddon", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754233077.6978452, "published_date": "2025-08-03 14:57:57"}, "pillage generator|th3emilis": {"mod_name": "Pillage Generator", "creator_name": "Th3<PERSON><PERSON><PERSON>", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Pillage_Generator_210_1754236112_46584qok.mcaddon", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754236128.097918, "published_date": "2025-08-03 15:48:48"}, "conqueror of villagers|dodofilms": {"mod_name": "Conqueror Of Villagers", "creator_name": "DodoFilms", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Conqueror_Of_Villagersbin_1754237037_vr42o6js.mcpack", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754237111.9194937, "published_date": "2025-08-03 16:05:11"}, "xp crystals find xp on your adventures|four worlds studios": {"mod_name": "XP Crystals (Find XP on your Adventures)", "creator_name": "Four Worlds Studios", "source_url": "", "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/XP_Crystals_Find_XP_on_your_Adventures_bin_1754239285_rb1qvcgs.mcpack", "mod_size": null, "version": null, "category": "Addons", "description": null, "published_at": 1754239415.727421, "published_date": "2025-08-03 16:43:35"}}